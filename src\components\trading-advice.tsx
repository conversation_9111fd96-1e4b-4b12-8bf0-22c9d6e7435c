"use client";

import React from "react";
import {
  TrendingUp,
  TrendingDown,
  Target,
  Shield,
  Clock,
  AlertTriangle,
  DollarSign,
  BarChart3,
  <PERSON>py,
  ExternalLink,
} from "lucide-react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>H<PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { toast } from "sonner";
import { MultiTimeframePredictionComponent } from "./multi-timeframe-prediction";
import { GameTheoryProbabilityAnalysis } from "./game-theory-probability-analysis";
import {
  MultiTimeframePrediction,
  GameTheoryAnalysis,
  ProbabilityAnalysis,
} from "@/types/trading";

interface TradingAdviceProps {
  advice: {
    action: "BUY" | "SELL" | "HOLD";
    entryPrice: number;
    quantity: number;
    stopLoss: number;
    takeProfit: number[];
    timeframe: string;
    confidence: number;
    reasoning: string;
    riskLevel: "LOW" | "MEDIUM" | "HIGH";
  };
  positionManagement: {
    initialPosition: number;
    addPositions: Array<{
      price: number;
      size: number;
      condition: string;
    }>;
    exitStrategy: {
      partialExits: Array<{
        price: number;
        percentage: number;
      }>;
      stopLoss: number;
      trailingStop: boolean;
    };
  };
  rollingStrategy: {
    basePosition: number;
    additionThreshold: number;
    maxPosition: number;
    profitTarget: number;
    stopLoss: number;
    trendConfirmation: boolean;
  };
  riskReward: number;
  symbol: string;
  multiTimeframePrediction?: MultiTimeframePrediction; // 新增多时间段预测
  gameTheoryAnalysis?: GameTheoryAnalysis; // 新增博弈论分析
  probabilityAnalysis?: ProbabilityAnalysis; // 新增概率论分析
  className?: string;
}

export function TradingAdvice({
  advice,
  positionManagement,
  rollingStrategy,
  riskReward,
  symbol,
  multiTimeframePrediction,
  gameTheoryAnalysis,
  probabilityAnalysis,
  className,
}: TradingAdviceProps) {
  const getActionColor = (action: string) => {
    switch (action) {
      case "BUY":
        return "bg-green-500 hover:bg-green-600";
      case "SELL":
        return "bg-red-500 hover:bg-red-600";
      default:
        return "bg-gray-500 hover:bg-gray-600";
    }
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case "BUY":
        return <TrendingUp className="h-4 w-4" />;
      case "SELL":
        return <TrendingDown className="h-4 w-4" />;
      default:
        return <BarChart3 className="h-4 w-4" />;
    }
  };

  const getRiskColor = (level: string) => {
    switch (level) {
      case "LOW":
        return "text-green-500 bg-green-50";
      case "MEDIUM":
        return "text-yellow-600 bg-yellow-50";
      case "HIGH":
        return "text-red-500 bg-red-50";
      default:
        return "text-gray-500 bg-gray-50";
    }
  };

  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success("📋 复制成功", {
        description: `${label}已复制到剪贴板`,
        duration: 2000,
      });
    } catch (error) {
      toast.error("❌ 复制失败", {
        description: "无法访问剪贴板，请手动复制",
        duration: 3000,
      });
    }
  };

  const formatPrice = (price: number) => {
    if (price >= 1) {
      return price.toFixed(4);
    } else if (price >= 0.01) {
      return price.toFixed(6);
    } else {
      return price.toFixed(8);
    }
  };

  const generateStrategyText = () => {
    const actionText =
      advice.action === "BUY"
        ? "买入"
        : advice.action === "SELL"
        ? "卖出"
        : "观望";
    const riskText =
      advice.riskLevel === "LOW"
        ? "低风险"
        : advice.riskLevel === "MEDIUM"
        ? "中等风险"
        : "高风险";

    let strategyText = `🎯 ${symbol} 交易策略\n\n`;
    strategyText += `📊 操作建议: ${actionText}\n`;
    strategyText += `💰 入场价格: $${formatPrice(advice.entryPrice)}\n`;
    strategyText += `🛡️ 止损价格: $${formatPrice(advice.stopLoss)}\n`;
    strategyText += `🎯 止盈目标: $${formatPrice(advice.takeProfit[0] || 0)}\n`;
    strategyText += `📈 建议数量: ${advice.quantity.toFixed(4)}\n`;
    strategyText += `⏰ 持有时间: ${advice.timeframe}\n`;
    strategyText += `🎲 信心度: ${advice.confidence}%\n`;
    strategyText += `⚠️ 风险等级: ${riskText}\n`;
    strategyText += `📊 风险收益比: 1:${riskReward.toFixed(1)}\n\n`;

    strategyText += `🔄 滚仓策略:\n`;
    strategyText += `• 基础仓位: ${rollingStrategy.basePosition}%\n`;
    strategyText += `• 最大仓位: ${rollingStrategy.maxPosition}%\n`;
    strategyText += `• 加仓阈值: ${rollingStrategy.additionThreshold}%\n`;
    strategyText += `• 趋势确认: ${
      rollingStrategy.trendConfirmation ? "已确认" : "待确认"
    }\n\n`;

    if (positionManagement.addPositions.length > 0) {
      strategyText += `📈 加仓计划:\n`;
      positionManagement.addPositions.slice(0, 3).forEach((position, index) => {
        strategyText += `• 加仓${index + 1}: $${formatPrice(position.price)} (${
          position.size
        }%) - ${position.condition}\n`;
      });
      strategyText += `\n`;
    }

    if (positionManagement.exitStrategy.partialExits.length > 0) {
      strategyText += `🎯 分批止盈:\n`;
      positionManagement.exitStrategy.partialExits.forEach((exit, index) => {
        strategyText += `• 止盈${index + 1}: $${formatPrice(exit.price)} (${
          exit.percentage
        }%)\n`;
      });
      strategyText += `\n`;
    }

    strategyText += `💡 策略说明:\n${advice.reasoning}\n\n`;
    strategyText += `⚠️ 风险提示:\n`;
    strategyText += `• 严格按照止损位执行\n`;
    strategyText += `• 不要在震荡市中盲目加仓\n`;
    strategyText += `• 保护已有利润，避免倒亏\n`;
    strategyText += `• 根据市场变化及时调整策略\n\n`;
    // 添加多时间段预测信息
    if (multiTimeframePrediction) {
      strategyText += `\n🔮 多时间段预测分析:\n`;
      strategyText += `• 当前价格: $${formatPrice(
        multiTimeframePrediction.currentPrice
      )}\n`;
      strategyText += `• 主导趋势: ${
        multiTimeframePrediction.overallAssessment.dominantTrend === "BULLISH"
          ? "看涨"
          : multiTimeframePrediction.overallAssessment.dominantTrend ===
            "BEARISH"
          ? "看跌"
          : "中性"
      }\n`;
      strategyText += `• 时间段一致性: ${multiTimeframePrediction.correlationAnalysis.timeframeAlignment}%\n`;
      strategyText += `• 趋势强度: ${multiTimeframePrediction.correlationAnalysis.trendStrength}%\n`;
      strategyText += `• 反转概率: ${multiTimeframePrediction.correlationAnalysis.reversal_probability}%\n`;

      if (multiTimeframePrediction.overallAssessment.conflictingSignals) {
        strategyText += `⚠️ 检测到不同时间段存在冲突信号\n`;
      }

      strategyText += `\n📊 分时段预测:\n`;

      // 短期预测
      const short = multiTimeframePrediction.predictions.short;
      strategyText += `🔥 短期 (${short.duration}):\n`;
      strategyText += `  • 趋势: ${
        short.trend === "BULLISH"
          ? "看涨"
          : short.trend === "BEARISH"
          ? "看跌"
          : "中性"
      } (信心度: ${short.confidence}%)\n`;
      strategyText += `  • 价格区间: $${formatPrice(
        short.targetPrice.low
      )} - $${formatPrice(short.targetPrice.high)}\n`;
      strategyText += `  • 最可能价格: $${formatPrice(
        short.targetPrice.most_likely
      )}\n`;
      strategyText += `  • 建议操作: ${
        short.tradingStrategy.action === "BUY"
          ? "买入"
          : short.tradingStrategy.action === "SELL"
          ? "卖出"
          : "观望"
      }\n`;
      if (short.tradingStrategy.action !== "HOLD") {
        strategyText += `  • 入场价: $${formatPrice(
          short.tradingStrategy.entryPrice
        )}\n`;
        strategyText += `  • 止损价: $${formatPrice(
          short.tradingStrategy.stopLoss
        )}\n`;
        strategyText += `  • 止盈价: $${formatPrice(
          short.tradingStrategy.takeProfit[0] || 0
        )}\n`;
        strategyText += `  • 建议仓位: ${short.tradingStrategy.positionSize}%\n`;
      }

      // 中期预测
      const medium = multiTimeframePrediction.predictions.medium;
      strategyText += `\n📈 中期 (${medium.duration}):\n`;
      strategyText += `  • 趋势: ${
        medium.trend === "BULLISH"
          ? "看涨"
          : medium.trend === "BEARISH"
          ? "看跌"
          : "中性"
      } (信心度: ${medium.confidence}%)\n`;
      strategyText += `  • 价格区间: $${formatPrice(
        medium.targetPrice.low
      )} - $${formatPrice(medium.targetPrice.high)}\n`;
      strategyText += `  • 最可能价格: $${formatPrice(
        medium.targetPrice.most_likely
      )}\n`;
      strategyText += `  • 建议操作: ${
        medium.tradingStrategy.action === "BUY"
          ? "买入"
          : medium.tradingStrategy.action === "SELL"
          ? "卖出"
          : "观望"
      }\n`;
      if (medium.tradingStrategy.action !== "HOLD") {
        strategyText += `  • 入场价: $${formatPrice(
          medium.tradingStrategy.entryPrice
        )}\n`;
        strategyText += `  • 止损价: $${formatPrice(
          medium.tradingStrategy.stopLoss
        )}\n`;
        strategyText += `  • 止盈价: $${formatPrice(
          medium.tradingStrategy.takeProfit[0] || 0
        )}\n`;
        strategyText += `  • 建议仓位: ${medium.tradingStrategy.positionSize}%\n`;
      }

      // 长期预测
      const long = multiTimeframePrediction.predictions.long;
      strategyText += `\n🎯 长期 (${long.duration}):\n`;
      strategyText += `  • 趋势: ${
        long.trend === "BULLISH"
          ? "看涨"
          : long.trend === "BEARISH"
          ? "看跌"
          : "中性"
      } (信心度: ${long.confidence}%)\n`;
      strategyText += `  • 价格区间: $${formatPrice(
        long.targetPrice.low
      )} - $${formatPrice(long.targetPrice.high)}\n`;
      strategyText += `  • 最可能价格: $${formatPrice(
        long.targetPrice.most_likely
      )}\n`;
      strategyText += `  • 建议操作: ${
        long.tradingStrategy.action === "BUY"
          ? "买入"
          : long.tradingStrategy.action === "SELL"
          ? "卖出"
          : "观望"
      }\n`;
      if (long.tradingStrategy.action !== "HOLD") {
        strategyText += `  • 入场价: $${formatPrice(
          long.tradingStrategy.entryPrice
        )}\n`;
        strategyText += `  • 止损价: $${formatPrice(
          long.tradingStrategy.stopLoss
        )}\n`;
        strategyText += `  • 止盈价: $${formatPrice(
          long.tradingStrategy.takeProfit[0] || 0
        )}\n`;
        strategyText += `  • 建议仓位: ${long.tradingStrategy.positionSize}%\n`;
      }

      strategyText += `\n💡 总体建议: ${multiTimeframePrediction.overallAssessment.recommendation}\n`;
    }

    // 添加博弈论分析信息
    if (gameTheoryAnalysis) {
      strategyText += `\n🎲 博弈论分析:\n`;

      // 市场参与者分析
      const makers = gameTheoryAnalysis.playerAnalysis.marketMakers;
      const institutions = gameTheoryAnalysis.playerAnalysis.institutions;
      const retailers = gameTheoryAnalysis.playerAnalysis.retailers;

      strategyText += `📊 市场参与者博弈:\n`;
      strategyText += `• 庄家策略: ${makers.strategy} (信心度: ${makers.confidence}%)\n`;
      strategyText += `• 庄家预期: ${
        makers.expectedMove === "UP"
          ? "上涨"
          : makers.expectedMove === "DOWN"
          ? "下跌"
          : "横盘"
      } (时间: ${
        makers.timeHorizon === "SHORT"
          ? "短期"
          : makers.timeHorizon === "MEDIUM"
          ? "中期"
          : "长期"
      })\n`;
      strategyText += `• 机构策略: ${institutions.strategy} (影响力: ${institutions.influence}%)\n`;
      strategyText += `• 机构仓位: ${
        institutions.positioning === "LONG"
          ? "多头"
          : institutions.positioning === "SHORT"
          ? "空头"
          : "中性"
      }\n`;
      strategyText += `• 散户行为: ${retailers.behavior} (情绪: ${retailers.sentiment}%)\n`;
      strategyText += `• 散户杠杆: ${
        retailers.leverage === "HIGH"
          ? "高杠杆"
          : retailers.leverage === "MEDIUM"
          ? "中等杠杆"
          : "低杠杆"
      } (爆仓风险: ${retailers.liquidationRisk}%)\n`;

      // 均衡分析
      const equilibrium = gameTheoryAnalysis.equilibriumAnalysis;
      strategyText += `\n⚖️ 博弈均衡分析:\n`;
      strategyText += `• 纳什均衡价格: $${formatPrice(
        equilibrium.nashEquilibrium.price
      )}\n`;
      strategyText += `• 均衡稳定性: ${equilibrium.nashEquilibrium.stability}%\n`;
      strategyText += `• 占优策略方: ${
        equilibrium.dominantStrategy.player === "MAKERS"
          ? "庄家"
          : equilibrium.dominantStrategy.player === "INSTITUTIONS"
          ? "机构"
          : "散户"
      }\n`;
      strategyText += `• 信息优势方: ${
        equilibrium.informationAsymmetry.advantage === "MAKERS"
          ? "庄家"
          : equilibrium.informationAsymmetry.advantage === "INSTITUTIONS"
          ? "机构"
          : "散户"
      } (不对称程度: ${equilibrium.informationAsymmetry.level}%)\n`;

      // 策略建议
      strategyText += `\n🎯 博弈策略建议:\n`;
      strategyText += `• 最优策略: ${
        gameTheoryAnalysis.strategicRecommendation.optimalStrategy === "BUY"
          ? "买入"
          : gameTheoryAnalysis.strategicRecommendation.optimalStrategy ===
            "SELL"
          ? "卖出"
          : gameTheoryAnalysis.strategicRecommendation.optimalStrategy ===
            "HOLD"
          ? "持有"
          : "等待"
      }\n`;
      strategyText += `• 策略理由: ${gameTheoryAnalysis.strategicRecommendation.reasoning}\n`;
      strategyText += `• 反制策略: ${gameTheoryAnalysis.strategicRecommendation.counterStrategy}\n`;
      strategyText += `• 风险缓解: ${gameTheoryAnalysis.strategicRecommendation.riskMitigation}\n`;
    }

    // 添加概率论分析信息
    if (probabilityAnalysis) {
      strategyText += `\n📊 概率论分析:\n`;

      // 贝叶斯推理
      const bayesian = probabilityAnalysis.bayesianInference;
      strategyText += `🔍 贝叶斯推理:\n`;
      strategyText += `• 先验概率 - 看涨: ${bayesian.priorProbability.bullish}%, 看跌: ${bayesian.priorProbability.bearish}%, 中性: ${bayesian.priorProbability.neutral}%\n`;
      strategyText += `• 后验概率 - 看涨: ${bayesian.posteriorProbability.bullish}%, 看跌: ${bayesian.posteriorProbability.bearish}%, 中性: ${bayesian.posteriorProbability.neutral}%\n`;
      strategyText += `• 推理信心度: ${bayesian.posteriorProbability.confidence}%\n`;

      // 条件概率
      const conditional = probabilityAnalysis.conditionalProbabilities;
      strategyText += `\n📈 条件概率分析:\n`;
      strategyText += `• 上涨概率 - 技术面: ${conditional.upMove.givenTechnicals}%, 成交量: ${conditional.upMove.givenVolume}%, 庄家活动: ${conditional.upMove.givenMakerActivity}%\n`;
      strategyText += `• 下跌概率 - 技术面: ${conditional.downMove.givenTechnicals}%, 成交量: ${conditional.downMove.givenVolume}%, 庄家活动: ${conditional.downMove.givenMakerActivity}%\n`;
      strategyText += `• 突破成功概率 - 成交量: ${conditional.breakoutSuccess.givenVolume}%, 动量: ${conditional.breakoutSuccess.givenMomentum}%, 庄家支持: ${conditional.breakoutSuccess.givenMakerSupport}%\n`;

      // 期望值分析
      const expected = probabilityAnalysis.expectedValue;
      strategyText += `\n💰 期望值分析:\n`;
      strategyText += `• 交易期望值: ${expected.tradeExpectation.toFixed(4)}\n`;
      strategyText += `• 风险调整收益: ${expected.riskAdjustedReturn.toFixed(
        4
      )}\n`;
      strategyText += `• 凯利公式仓位: ${expected.kellyPercentage}%\n`;
      strategyText += `• 最优仓位大小: ${expected.optimalPositionSize}%\n`;
      strategyText += `• 预期回撤: ${expected.expectedDrawdown.toFixed(2)}%\n`;

      // 风险概率
      const risk = probabilityAnalysis.riskProbabilities;
      strategyText += `\n⚠️ 风险概率评估:\n`;
      strategyText += `• 止损触发概率: ${risk.stopLossHit}%\n`;
      strategyText += `• 止盈达成概率: ${risk.takeProfitHit}%\n`;
      strategyText += `• 最大回撤超限概率: ${risk.maxDrawdownExceeded}%\n`;
      strategyText += `• 极端事件概率: ${risk.extremeEvent}%\n`;
      strategyText += `• 爆仓风险概率: ${risk.liquidationRisk}%\n`;
    }

    strategyText += `\n📅 生成时间: ${new Date().toLocaleString("zh-CN")}`;

    return strategyText;
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 主要交易建议 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Target className="h-5 w-5" />
              <span>交易建议 - {symbol}</span>
            </div>
            <Badge className={`${getActionColor(advice.action)} text-white`}>
              {getActionIcon(advice.action)}
              <span className="ml-1">
                {advice.action === "BUY"
                  ? "买入"
                  : advice.action === "SELL"
                  ? "卖出"
                  : "观望"}
              </span>
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 信心度和风险等级 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span className="text-sm text-gray-500">信心度</span>
              <Progress value={advice.confidence} className="w-24" />
              <span className="text-sm font-medium">{advice.confidence}%</span>
            </div>
            <Badge className={getRiskColor(advice.riskLevel)}>
              {advice.riskLevel === "LOW"
                ? "低风险"
                : advice.riskLevel === "MEDIUM"
                ? "中等风险"
                : "高风险"}
            </Badge>
          </div>

          <Separator />

          {/* 关键价格信息 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-sm text-gray-500 mb-1">入场价格</p>
              <div className="flex items-center justify-center space-x-1">
                <p className="font-semibold">
                  ${formatPrice(advice.entryPrice)}
                </p>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() =>
                    copyToClipboard(advice.entryPrice.toString(), "入场价格")
                  }
                >
                  <Copy className="h-3 w-3" />
                </Button>
              </div>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-500 mb-1">止损价格</p>
              <div className="flex items-center justify-center space-x-1">
                <p className="font-semibold text-red-500">
                  ${formatPrice(advice.stopLoss)}
                </p>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() =>
                    copyToClipboard(advice.stopLoss.toString(), "止损价格")
                  }
                >
                  <Copy className="h-3 w-3" />
                </Button>
              </div>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-500 mb-1">止盈目标</p>
              <div className="flex items-center justify-center space-x-1">
                <p className="font-semibold text-green-500">
                  ${formatPrice(advice.takeProfit[0] || 0)}
                </p>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() =>
                    copyToClipboard(
                      (advice.takeProfit[0] || 0).toString(),
                      "止盈价格"
                    )
                  }
                >
                  <Copy className="h-3 w-3" />
                </Button>
              </div>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-500 mb-1">风险收益比</p>
              <p className="font-semibold">1:{riskReward.toFixed(1)}</p>
            </div>
          </div>

          {/* 时间框架和数量 */}
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-500">持有时间:</span>
              <span className="font-medium">{advice.timeframe}</span>
            </div>
            <div className="flex items-center space-x-2">
              <DollarSign className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-500">建议数量:</span>
              <span className="font-medium">{advice.quantity.toFixed(4)}</span>
            </div>
          </div>

          {/* 推理说明 */}
          <div className="bg-blue-50 p-3 rounded-lg">
            <p className="text-sm text-blue-800">{advice.reasoning}</p>
          </div>
        </CardContent>
      </Card>

      {/* 滚仓策略详情 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5" />
            <span>滚仓策略</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 策略概览 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-sm text-gray-500">基础仓位</p>
              <p className="text-lg font-semibold">
                {rollingStrategy.basePosition}%
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-500">最大仓位</p>
              <p className="text-lg font-semibold">
                {rollingStrategy.maxPosition}%
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-500">加仓阈值</p>
              <p className="text-lg font-semibold">
                {rollingStrategy.additionThreshold}%
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-500">趋势确认</p>
              <Badge
                variant={
                  rollingStrategy.trendConfirmation ? "default" : "secondary"
                }
              >
                {rollingStrategy.trendConfirmation ? "已确认" : "待确认"}
              </Badge>
            </div>
          </div>

          <Separator />

          {/* 加仓计划 */}
          <div>
            <h4 className="font-medium mb-3 flex items-center space-x-2">
              <TrendingUp className="h-4 w-4" />
              <span>加仓计划</span>
            </h4>
            <div className="space-y-2">
              {positionManagement.addPositions
                .slice(0, 3)
                .map((position, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-2 bg-gray-50 rounded"
                  >
                    <span className="text-sm font-medium">
                      加仓 {index + 1}
                    </span>
                    <span className="text-sm">
                      ${formatPrice(position.price)}
                    </span>
                    <Badge variant="outline">{position.size}%</Badge>
                    <span className="text-xs text-gray-500 max-w-32 truncate">
                      {position.condition}
                    </span>
                  </div>
                ))}
            </div>
          </div>

          <Separator />

          {/* 分批止盈 */}
          <div>
            <h4 className="font-medium mb-3 flex items-center space-x-2">
              <Target className="h-4 w-4" />
              <span>分批止盈</span>
            </h4>
            <div className="space-y-2">
              {positionManagement.exitStrategy.partialExits.map(
                (exit, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-2 bg-green-50 rounded"
                  >
                    <span className="text-sm font-medium">
                      止盈 {index + 1}
                    </span>
                    <span className="text-sm">${formatPrice(exit.price)}</span>
                    <Badge variant="outline" className="text-green-600">
                      {exit.percentage}%
                    </Badge>
                  </div>
                )
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 风险提示 */}
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          <strong>风险提示:</strong>
          滚仓策略需要严格的纪律性和风险控制。请确保： 1) 严格按照止损位执行；
          2) 不要在震荡市中盲目加仓； 3) 保护已有利润，避免倒亏； 4)
          根据市场变化及时调整策略。
        </AlertDescription>
      </Alert>

      {/* 多时间段预测 */}
      {multiTimeframePrediction && (
        <MultiTimeframePredictionComponent
          prediction={multiTimeframePrediction}
          className="mt-6"
        />
      )}

      {/* 博弈论和概率论分析 */}
      {gameTheoryAnalysis && probabilityAnalysis && (
        <GameTheoryProbabilityAnalysis
          gameTheory={gameTheoryAnalysis}
          probability={probabilityAnalysis}
          symbol={symbol}
          className="mt-6"
        />
      )}

      {/* 操作按钮 */}
      <div className="flex space-x-3">
        <Button
          className={getActionColor(advice.action)}
          disabled={advice.action === "HOLD"}
        >
          {getActionIcon(advice.action)}
          <span className="ml-2">
            {advice.action === "BUY"
              ? "执行买入"
              : advice.action === "SELL"
              ? "执行卖出"
              : "暂时观望"}
          </span>
        </Button>
        <Button variant="outline">
          <ExternalLink className="h-4 w-4 mr-2" />
          查看详细分析
        </Button>
        <Button
          variant="outline"
          onClick={() => copyToClipboard(generateStrategyText(), "完整策略")}
        >
          <Copy className="h-4 w-4 mr-2" />
          复制策略
        </Button>
      </div>
    </div>
  );
}
